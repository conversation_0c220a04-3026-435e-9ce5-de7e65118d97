# 🎬 Bulk Video Transcription Tool

A simple, fast, and reliable tool for transcribing multiple videos at once using OpenAI's Whisper model.

## ✨ Features

- **Bulk Processing**: Transcribe all videos in a folder with one command
- **Smart Skipping**: Automatically skips already transcribed videos
- **Multiple Formats**: Supports MP4, MOV, AVI, MKV, WMV, FLV, WEBM, M4V
- **Clean Output**: Creates .txt files with same name as video files
- **Progress Tracking**: Shows real-time progress and statistics
- **Fast & Offline**: Uses faster-whisper for speed, works completely offline

## 🚀 Quick Start

### 1. Setup (one-time)
```bash
# Clone or download this tool
cd /path/to/transcribe/

# Create virtual environment
python3 -m venv venv

# Install dependencies
source venv/bin/activate
pip install faster-whisper
```

### 2. Usage

#### Simple Usage (current directory)
```bash
# Transcribe all videos in current directory
./transcribe

# Or use Python directly
python3 bulk_transcribe.py
```

#### Specify a folder
```bash
# Transcribe all videos in a specific folder
./transcribe /path/to/your/videos/

# Or use Python directly
python3 bulk_transcribe.py /path/to/your/videos/
```

## 📁 Example

```bash
# Your video folder structure:
videos/
├── meeting1.mp4
├── lecture2.mov
├── interview3.avi
└── presentation4.mkv

# Run the tool:
./transcribe videos/

# Result:
videos/
├── meeting1.mp4
├── meeting1.txt          ← New transcript file
├── lecture2.mov
├── lecture2.txt          ← New transcript file
├── interview3.avi
├── interview3.txt        ← New transcript file
├── presentation4.mkv
└── presentation4.txt     ← New transcript file
```

## 📝 Transcript Format

Each transcript file contains:
- Video filename and metadata
- Detected language and confidence
- Video duration
- Timestamped segments
- Clean transcript at the end

Example transcript:
```
Video: meeting1.mp4
Language: en (confidence: 0.95)
Duration: 120.45 seconds
============================================================

[0.00s -> 5.20s] Welcome everyone to today's meeting.
[5.20s -> 12.40s] Let's start by reviewing the quarterly results.
[12.40s -> 18.60s] As you can see from the chart, we've exceeded our targets.

============================================================
CLEAN TRANSCRIPT:
============================================================
Welcome everyone to today's meeting. Let's start by reviewing the quarterly results. As you can see from the chart, we've exceeded our targets.
```

## ⚡ Performance

- **Speed**: ~2-5 seconds per minute of video (depends on your CPU)
- **Memory**: Uses optimized int8 quantization for lower memory usage
- **CPU Only**: No GPU required, works on any Linux system

## 🔧 Supported Formats

- **Video**: MP4, MOV, AVI, MKV, WMV, FLV, WEBM, M4V
- **Audio**: Any format supported by FFmpeg (automatically extracted)

## 🛠️ Advanced Usage

### Custom Python Usage
```python
from bulk_transcribe import transcribe_video, WhisperModel

# Initialize model once for multiple videos
model = WhisperModel("base", device="cpu", compute_type="int8")

# Transcribe single video
success = transcribe_video("video.mp4", model)
```

### Different Whisper Models
Edit `bulk_transcribe.py` and change the model size:
- `tiny` - Fastest, least accurate
- `base` - Good balance (default)
- `small` - Better accuracy
- `medium` - Even better accuracy
- `large` - Best accuracy, slowest

## 🐛 Troubleshooting

### "No video files found"
- Check that your videos have supported extensions
- Make sure you're in the right directory

### "Virtual environment not found"
```bash
python3 -m venv venv
source venv/bin/activate
pip install faster-whisper
```

### "Permission denied"
```bash
chmod +x transcribe
chmod +x bulk_transcribe.py
```

### Slow transcription
- Use a smaller model (`tiny` instead of `base`)
- Close other applications to free up CPU
- Consider using a machine with more CPU cores

## 📊 Example Output

```
🎬 BULK VIDEO TRANSCRIPTION TOOL
==================================================
📁 Folder: /home/<USER>/videos
📹 Found 5 video files
✅ Already transcribed: 2
🔄 Need transcription: 3

🚀 Starting transcription of 3 videos...
🤖 Loading Whisper model (this may take a moment)...
✅ Model loaded successfully!

[1/3] 🎬 Processing: meeting.mp4
✅ Saved: meeting.txt

[2/3] 🎬 Processing: lecture.mov
✅ Saved: lecture.txt

[3/3] 🎬 Processing: interview.avi
✅ Saved: interview.txt

==================================================
🎉 TRANSCRIPTION COMPLETE!
==================================================
✅ Successful: 3
❌ Failed: 0
⏱️  Total time: 2m 15s
📊 Average per video: 45.0s
📁 Transcripts saved in: /home/<USER>/videos
```

## 🎯 Perfect for:

- Meeting recordings
- Lecture videos
- Interview transcriptions
- Podcast episodes
- YouTube video content
- Any video with speech content

---

**Made with ❤️ for bulk video transcription needs!**
