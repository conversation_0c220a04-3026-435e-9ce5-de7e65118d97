#!/bin/bash

# Bulk Video Transcription Tool - Easy Wrapper Script
# Usage: ./transcribe [folder_path]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_PATH="$SCRIPT_DIR/venv"
PYTHON_SCRIPT="$SCRIPT_DIR/bulk_transcribe.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎬 Bulk Video Transcription Tool${NC}"
echo -e "${BLUE}=================================${NC}"

# Check if virtual environment exists
if [ ! -d "$VENV_PATH" ]; then
    echo -e "${RED}❌ Virtual environment not found!${NC}"
    echo -e "${YELLOW}Please run setup first:${NC}"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install faster-whisper"
    exit 1
fi

# Check if Python script exists
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo -e "${RED}❌ bulk_transcribe.py not found!${NC}"
    exit 1
fi

# Activate virtual environment and run the script
echo -e "${GREEN}🚀 Starting transcription...${NC}"
echo ""

source "$VENV_PATH/bin/activate"
python3 "$PYTHON_SCRIPT" "$@"

echo ""
echo -e "${GREEN}✅ Done!${NC}"
