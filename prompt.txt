I am selling a product and i am going to drop you all the ads transcribed for my competitor and make me a better VSL between 2-4 minutes
Note: Don’t forget to mention a visual scene for each one
note: Create logical roadmaps to connect my product’s features to the solution. i will give you a
bad example with heat shoulder product just to understand what i mean Heat → dilates blood
vessels → improves blood flow → flushes waste product from the area & brings nutrients &
oxygen → heals & relaxes stiff muscles → relieves pain
note: I am going to use istockphoto for video clips and animations, and tiktok for clips and to search for my product in use, youtube for clips && to search my product in use, and google veo 2 to generate clips & animations if necesary... ( so i will need you to give me at the end of the VSL script a multiple keywords for each platform to look at and try to find the exact same clip for each part (because with 1 keyword each platform it's hard to find, sometimes you need to use multiple keywords, sentence etc...)) and for google veo you will need to generate me a prompt for example check the prompt for animation inside fitness_pain_relief_animations.json and the prompt for clips inside of  