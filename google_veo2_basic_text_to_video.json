{"last_node_id": 8, "last_link_id": 12, "nodes": [{"id": 1, "type": "VeoTextToVideo", "pos": [400, 100], "size": [400, 280], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 1}], "outputs": [{"name": "video_paths", "type": "VEO_VIDEO", "links": [2]}], "properties": {"Node name for S&R": "VeoTextToVideo"}, "widgets_values": ["", "16:9", "allow_adult", 8, "AIzaSyByp-3dEPPV188sTGX6rnCkj2qwvU_F4xY", ""], "title": "🎬 Google Veo 2 - ULTRA 4K QUALITY"}, {"id": 2, "type": "PrimitiveStringMultiline", "pos": [50, 100], "size": [300, 200], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [4]}], "properties": {"Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": ["3D medical animation showing radiofrequency waves penetrating skin layers, cross-section view of dermis and epidermis, collagen fibers tightening and regenerating, scientific visualization of skin rejuvenation process"], "title": "🧬 MEDICAL ANIMATION (What You Want)"}, {"id": 5, "type": "PrimitiveStringMultiline", "pos": [50, 350], "size": [300, 200], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [5]}], "properties": {"Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": ["Medium wide shot, camera pulled back to show full upper body and head with space around subject, professional advertising angle perfect for social media ads, dynamic composition with room for text overlays and subtitles, engaging framing that leaves space at top and bottom for graphics, smooth camera movement, professional lighting for Facebook and Instagram ads, commercial-quality cinematography with social media formatting"], "title": "🎬 AD CAMERA ANGLES (Don't Change)"}, {"id": 6, "type": "StringConcatenate", "pos": [400, 250], "size": [300, 150], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "string_a", "type": "STRING", "link": 5}, {"name": "string_b", "type": "STRING", "link": 4}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1]}], "properties": {"Node name for S&R": "StringConcatenate"}, "widgets_values": ["", ": ", ""], "title": "🔗 Combine Quality + Content"}, {"id": 3, "type": "VeoToVHS", "pos": [850, 100], "size": [200, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "video_paths", "type": "VEO_VIDEO", "link": 2}], "outputs": [{"name": "images", "type": "IMAGE", "links": [3]}], "properties": {"Node name for S&R": "VeoToVHS"}, "widgets_values": [], "title": "Convert to Images"}, {"id": 4, "type": "VHS_VideoCombine", "pos": [1100, 100], "size": [320, 800], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 30, "loop_count": 0, "filename_prefix": "veo2_ultra4k_", "format": "video/h264-mp4", "pix_fmt": "yuv420p10le", "crf": 15, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "veo2_ultra4k_00001.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 30}}}, "title": "💾 SAVE ULTRA 4K VIDEO"}], "links": [[1, 6, 0, 1, 0, "STRING"], [2, 1, 0, 3, 0, "VEO_VIDEO"], [3, 3, 0, 4, 0, "IMAGE"], [4, 2, 0, 6, 1, "STRING"], [5, 5, 0, 6, 0, "STRING"]], "groups": [{"title": "🎬 GOOGLE VEO 2 - ULTRA 4K QUALITY GENERATOR", "bounding": [20, 50, 1420, 950], "color": "#FF4500", "font_size": 32}], "config": {}, "extra": {}, "version": 0.4}