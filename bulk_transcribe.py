#!/usr/bin/env python3
"""
Bulk Video Transcription Tool

Usage:
    python3 bulk_transcribe.py [folder_path]
    
If no folder is specified, it will process videos in the current directory.

Features:
- Processes all video files in a folder
- Creates .txt files with same name as video
- Skips already transcribed videos
- Shows progress and statistics
- Supports: mp4, mov, avi, mkv, wmv, flv, webm
"""

import os
import sys
import time
from pathlib import Path
from faster_whisper import WhisperModel
import glob

# Supported video formats
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v']

def find_video_files(folder_path):
    """Find all video files in the specified folder"""
    video_files = []
    
    for ext in VIDEO_EXTENSIONS:
        pattern = os.path.join(folder_path, f"*{ext}")
        video_files.extend(glob.glob(pattern, recursive=False))
        # Also check uppercase extensions
        pattern = os.path.join(folder_path, f"*{ext.upper()}")
        video_files.extend(glob.glob(pattern, recursive=False))
    
    return sorted(video_files)

def get_transcript_path(video_path):
    """Get the transcript file path for a video"""
    video_file = Path(video_path)
    return video_file.parent / f"{video_file.stem}.txt"

def is_already_transcribed(video_path):
    """Check if video is already transcribed"""
    transcript_path = get_transcript_path(video_path)
    return transcript_path.exists()

def transcribe_video(video_path, model):
    """Transcribe a single video file"""
    try:
        transcript_path = get_transcript_path(video_path)
        
        print(f"🎬 Processing: {os.path.basename(video_path)}")
        
        # Transcribe the video
        segments, info = model.transcribe(video_path, beam_size=5)
        
        # Write transcript to file
        with open(transcript_path, 'w', encoding='utf-8') as f:
            f.write(f"Video: {os.path.basename(video_path)}\n")
            f.write(f"Language: {info.language} (confidence: {info.language_probability:.2f})\n")
            f.write(f"Duration: {info.duration:.2f} seconds\n")
            f.write("=" * 60 + "\n\n")
            
            transcript_text = ""
            for segment in segments:
                timestamp = f"[{segment.start:.2f}s -> {segment.end:.2f}s]"
                line = f"{timestamp} {segment.text.strip()}\n"
                f.write(line)
                transcript_text += segment.text.strip() + " "
            
            # Add a clean version at the end
            if transcript_text.strip():
                f.write("\n" + "=" * 60 + "\n")
                f.write("CLEAN TRANSCRIPT:\n")
                f.write("=" * 60 + "\n")
                f.write(transcript_text.strip())
        
        print(f"✅ Saved: {os.path.basename(transcript_path)}")
        return True
        
    except Exception as e:
        print(f"❌ Error processing {os.path.basename(video_path)}: {str(e)}")
        return False

def format_time(seconds):
    """Format seconds into human readable time"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds//60:.0f}m {seconds%60:.0f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours:.0f}h {minutes:.0f}m"

def main():
    """Main function"""
    # Get folder path from command line or use current directory
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    else:
        folder_path = "."
    
    # Validate folder
    if not os.path.isdir(folder_path):
        print(f"❌ Error: '{folder_path}' is not a valid directory")
        sys.exit(1)
    
    folder_path = os.path.abspath(folder_path)
    
    print("🎬 BULK VIDEO TRANSCRIPTION TOOL")
    print("=" * 50)
    print(f"📁 Folder: {folder_path}")
    
    # Find video files
    video_files = find_video_files(folder_path)
    
    if not video_files:
        print("❌ No video files found!")
        print(f"   Supported formats: {', '.join(VIDEO_EXTENSIONS)}")
        sys.exit(1)
    
    # Check which videos need transcription
    videos_to_process = []
    already_done = []
    
    for video_path in video_files:
        if is_already_transcribed(video_path):
            already_done.append(video_path)
        else:
            videos_to_process.append(video_path)
    
    print(f"📹 Found {len(video_files)} video files")
    print(f"✅ Already transcribed: {len(already_done)}")
    print(f"🔄 Need transcription: {len(videos_to_process)}")
    
    if already_done:
        print("\n📋 Already transcribed:")
        for video in already_done:
            print(f"   ✓ {os.path.basename(video)}")
    
    if not videos_to_process:
        print("\n🎉 All videos are already transcribed!")
        sys.exit(0)
    
    print(f"\n🚀 Starting transcription of {len(videos_to_process)} videos...")
    
    # Initialize Whisper model
    print("🤖 Loading Whisper model (this may take a moment)...")
    model = WhisperModel("base", device="cpu", compute_type="int8")
    print("✅ Model loaded successfully!")
    
    # Process videos
    start_time = time.time()
    successful = 0
    failed = 0
    
    for i, video_path in enumerate(videos_to_process, 1):
        print(f"\n[{i}/{len(videos_to_process)}] ", end="")
        
        if transcribe_video(video_path, model):
            successful += 1
        else:
            failed += 1
    
    # Summary
    total_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("🎉 TRANSCRIPTION COMPLETE!")
    print("=" * 50)
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"⏱️  Total time: {format_time(total_time)}")
    
    if successful > 0:
        avg_time = total_time / successful
        print(f"📊 Average per video: {format_time(avg_time)}")
    
    print(f"📁 Transcripts saved in: {folder_path}")

if __name__ == "__main__":
    main()
